import json
from pathlib import Path

from metaloader_rest_api.flow_resource.flow_resource_processor_unit_rdv import (
    RdvFlowResourceProcessorUnit,
)
from metaloader_rest_api.local_file_profider.local_file_provider import (
    LocalFileProvider,
)
from metaloader_rest_api.yaml.yaml_loader import load_yaml


def load_flow_data(path):
    with open(path, "r", encoding="utf-8") as file:
        return load_yaml(file.read())


processor = RdvFlowResourceProcessorUnit(
    file_provider=LocalFileProvider(
        path=Path("C:\\Users\\<USER>\\Projects\\ceh-etl\\"),
    ),
)

flow_yaml = load_flow_data(
    "C:\\Users\\<USER>\\Projects\\ceh-etl\\"
    "general_ledger\\"
    "src_rdv\\"
    "schema\\"
    "work_flows\\"
    # "wf_acqb_ods_rdv_mart_transaction_docs_doc_acqb.yaml"
    # "wf_uni_map.yaml"
    "wf_uni_ref.yaml"
)

with open("rdv_work_flow_resources.json", "w") as f:
    flow_resources = []
    for flow_resource in processor.process(-1, "test", flow_yaml):
        del flow_resource["flow_id"]  # noqa
        flow_resource["type"] = flow_resource["type"].name  # noqa
        flow_resources.append(flow_resource)
    print(f"{len(flow_resources)=}")
    json.dump(flow_resources, f, indent=2)
