type: WORK_FLOW
targets:
  - short_name: tgt_alias_1
    resource_cd: test.resource.tgt.1
    table: table_1
    schema: rdv
sources:
  - short_name: src_alias_1
    type: DB_TABLE
    resource_cd: test.resource.src.1
  - short_name: src_uni_map
    type: COMPUTED_TABLE
    object: stg_uni_map
    sub_sources_map: {}
mappings:
  marts:
    - algorithm_uid: ALGORITHM_UNI_MAP_TEST
      target: tgt_alias_1
      source: src_uni_map
    - algorithm_uid: ALGORITHM_UNI_MAP_TEST
      target: tgt_alias_1
      source: src_alias_1
